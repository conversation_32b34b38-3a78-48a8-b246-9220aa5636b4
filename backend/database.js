require('dotenv').config();

const { Pool } = require('pg');
const logger = require('./logger');

class PostgresConnection {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  static get instance() {
    if (!this._instance) {
      this._instance = new PostgresConnection();
    }
    return this._instance;
  }

  async connect() {
    try {
      // Check if DATABASE_URL is provided
      if (!process.env.DATABASE_URL) {
        throw new Error('DATABASE_URL environment variable is required. Please set it in your .env file.');
      }

      // Database configuration for Supabase/PostgreSQL
      const config = {
        connectionString: process.env.DATABASE_URL,
        ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
        max: 10,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
      };

      this.pool = new Pool(config);

      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      this.isConnected = true;
      logger.info('PostgreSQL database connected successfully');

    } catch (error) {
      logger.error('Database connection failed:', error);

      // Provide helpful error message for common issues
      if (error.message.includes('DATABASE_URL')) {
        logger.error('Please configure your DATABASE_URL in .env file');
        logger.error('Example: DATABASE_URL=postgresql://username:password@host:port/database');
      }

      throw error;
    }
  }



  async execute(sql, params = []) {
    if (!this.isConnected) {
      await this.connect();
    }

    try {
      logger.info('Executing query:', { sql, params });
      const result = await this.pool.query(sql, params);
      return result.rows;
    } catch (error) {
      logger.error('Query execution failed:', { sql, params, error: error.message });
      throw error;
    }
  }

  static async beginTransaction() {
    const client = await PostgresConnection.instance.pool.connect();
    await client.query('BEGIN');

    return {
      execute: async (sql, params) => {
        logger.info(`Executing query with client processID: ${client.processID}`);
        const result = await client.query(sql, params);
        return result.rows;
      },
      commit: async () => {
        try {
          await client.query('COMMIT');
          logger.info(`Committing transaction with client processID: ${client.processID}`);
        } finally {
          client.release();
        }
      },
      rollback: async () => {
        try {
          await client.query('ROLLBACK');
          logger.info(`Rolling back transaction with client processID: ${client.processID}`);
        } finally {
          client.release();
        }
      }
    };
  }
}

// Database operations
async function saveSession(sessionData) {
  const { id, start_time, end_time, duration, milk_quantity, created_at } = sessionData;

  const sql = `
    INSERT INTO milking_sessions (id, start_time, end_time, duration, milk_quantity, created_at)
    VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING *
  `;

  const params = [id, start_time, end_time, duration, milk_quantity, created_at];

  const results = await PostgresConnection.instance.execute(sql, params);

  // Return the saved session
  return results[0];
}

async function getAllSessions() {
  const sql = `
    SELECT id, start_time, end_time, duration, milk_quantity, created_at
    FROM milking_sessions
    ORDER BY created_at DESC
  `;

  const results = await PostgresConnection.instance.execute(sql);
  return results;
}

// Initialize database connection
PostgresConnection.instance.connect().catch(error => {
  logger.error('Failed to initialize database:', error);
  process.exit(1);
});

module.exports = {
  PostgresConnection,
  saveSession,
  getAllSessions
};
