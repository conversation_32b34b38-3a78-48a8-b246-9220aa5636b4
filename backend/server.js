// Load environment variables
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const moment = require('moment-timezone');
const logger = require('./logger');
const db = require('./database');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Request logging middleware
app.use((req, _res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    body: req.body,
    query: req.query
  });
  next();
});

// Routes
app.get('/health', (_req, res) => {
  res.json({ status: 'OK', timestamp: moment.tz('UTC').toISOString() });
});

// GET /sessions - Retrieve all milking sessions
app.get('/sessions', async (_req, res) => {
  try {
    const sessions = await db.getAllSessions();
    logger.info(`Retrieved ${sessions.length} sessions`);
    res.json(sessions);
  } catch (error) {
    logger.error('Error retrieving sessions:', error);
    res.status(500).json({
      error: 'Failed to retrieve sessions',
      message: error.message
    });
  }
});

// POST /sessions - Save milking session details
app.post('/sessions', async (req, res) => {
  try {
    const { start_time, end_time, duration, milk_quantity } = req.body;

    // Validation
    if (!start_time || !end_time || !duration || milk_quantity === undefined) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['start_time', 'end_time', 'duration', 'milk_quantity']
      });
    }

    // Validate duration is positive
    if (duration <= 0) {
      return res.status(400).json({
        error: 'Duration must be positive'
      });
    }

    // Validate milk quantity is non-negative
    if (milk_quantity < 0) {
      return res.status(400).json({
        error: 'Milk quantity cannot be negative'
      });
    }

    // Convert times to UTC
    const startTimeUTC = moment.tz(start_time, 'UTC').toISOString();
    const endTimeUTC = moment.tz(end_time, 'UTC').toISOString();

    const sessionData = {
      id: uuidv4(),
      start_time: startTimeUTC,
      end_time: endTimeUTC,
      duration: parseInt(duration),
      milk_quantity: parseFloat(milk_quantity),
      created_at: moment.tz('UTC').toISOString()
    };

    const savedSession = await db.saveSession(sessionData);
    logger.info('Session saved successfully:', { sessionId: savedSession.id });

    res.status(201).json(savedSession);
  } catch (error) {
    logger.error('Error saving session:', error);
    res.status(500).json({
      error: 'Failed to save session',
      message: error.message
    });
  }
});

// Error handling middleware
app.use((error, _req, res, _next) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// 404 handler
app.use((_req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Start server (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
    console.log(`🚀 Backend server running on http://localhost:${PORT}`);
  });
}

// Export for Vercel
module.exports = app;
