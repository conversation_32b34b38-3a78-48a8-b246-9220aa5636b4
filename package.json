{"name": "milking-tracker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "backend": "node backend/server.js", "backend:dev": "nodemon backend/server.js"}, "dependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "moment-timezone": "^0.6.0", "next": "15.3.4", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "nodemon": "^3.1.10", "tailwindcss": "^4", "typescript": "^5"}}