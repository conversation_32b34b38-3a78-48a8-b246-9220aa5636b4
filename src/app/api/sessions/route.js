import { Pool } from 'pg';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment-timezone';

// Database connection
let pool;

function getPool() {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });
  }
  return pool;
}

// Database operations
async function saveSession(sessionData) {
  const { id, start_time, end_time, duration, milk_quantity, created_at } = sessionData;
  
  const sql = `
    INSERT INTO milking_sessions (id, start_time, end_time, duration, milk_quantity, created_at)
    VALUES ($1, $2, $3, $4, $5, $6)
    RETURNING *
  `;
  
  const params = [id, start_time, end_time, duration, milk_quantity, created_at];
  
  const pool = getPool();
  const result = await pool.query(sql, params);
  
  return result.rows[0];
}

async function getAllSessions() {
  const sql = `
    SELECT id, start_time, end_time, duration, milk_quantity, created_at
    FROM milking_sessions
    ORDER BY created_at DESC
  `;
  
  const pool = getPool();
  const result = await pool.query(sql);
  return result.rows;
}

export async function GET() {
  try {
    const sessions = await getAllSessions();
    console.log(`Retrieved ${sessions.length} sessions`);
    return Response.json(sessions);
  } catch (error) {
    console.error('API Error:', error);
    return Response.json(
      { error: 'Failed to retrieve sessions', message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const { start_time, end_time, duration, milk_quantity } = await request.json();

    // Validation
    if (!start_time || !end_time || !duration || milk_quantity === undefined) {
      return Response.json(
        {
          error: 'Missing required fields',
          required: ['start_time', 'end_time', 'duration', 'milk_quantity']
        },
        { status: 400 }
      );
    }

    // Validate duration is positive
    if (duration <= 0) {
      return Response.json(
        { error: 'Duration must be positive' },
        { status: 400 }
      );
    }

    // Validate milk quantity is non-negative
    if (milk_quantity < 0) {
      return Response.json(
        { error: 'Milk quantity cannot be negative' },
        { status: 400 }
      );
    }

    // Convert times to UTC
    const startTimeUTC = moment.tz(start_time, 'UTC').toISOString();
    const endTimeUTC = moment.tz(end_time, 'UTC').toISOString();

    const sessionData = {
      id: uuidv4(),
      start_time: startTimeUTC,
      end_time: endTimeUTC,
      duration: parseInt(duration),
      milk_quantity: parseFloat(milk_quantity),
      created_at: moment.tz('UTC').toISOString()
    };

    const savedSession = await saveSession(sessionData);
    console.log('Session saved successfully:', { sessionId: savedSession.id });
    
    return Response.json(savedSession, { status: 201 });
  } catch (error) {
    console.error('API Error:', error);
    return Response.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}
