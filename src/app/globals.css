@import 'tailwindcss';

:root {
  --background: #fefefe;
  --foreground: #2d3748;
  --primary: #4299e1;
  --primary-light: #63b3ed;
  --primary-dark: #3182ce;
  --secondary: #f7fafc;
  --accent: #ed8936;
  --success: #48bb78;
  --warning: #ed8936;
  --border: #e2e8f0;
  --shadow: rgba(0, 0, 0, 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  line-height: 1.6;
}

/* Custom animations for smooth interactions */
@keyframes gentle-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes soft-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-gentle-bounce {
  animation: gentle-bounce 2s infinite;
}

.animate-soft-pulse {
  animation: soft-pulse 2s infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Round button styles */
.btn-round {
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px var(--shadow);
}

.btn-round:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--shadow);
}

.btn-round:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px var(--shadow);
}

/* Card styles */
.card-light {
  background: white;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border);
  transition: all 0.3s ease;
}

.card-light:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* Timer circle */
.timer-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.timer-circle.active {
  animation: soft-pulse 2s infinite;
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 8px 32px rgba(72, 187, 120, 0.4);
}

.timer-circle.paused {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  box-shadow: 0 8px 32px rgba(237, 137, 54, 0.3);
}

/* Custom animations */
@keyframes pulse-gentle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes bounce-subtle {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s ease-in-out infinite;
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out forwards;
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(5, 150, 105, 0.2);
}

/* Timer display */
.timer-display {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.05em;
}
