@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #1f2937;
  --primary: #3b82f6;
  --primary-light: #60a5fa;
  --primary-dark: #2563eb;
  --secondary: #f8fafc;
  --accent: #10b981;
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --border: #e5e7eb;
  --shadow: rgba(0, 0, 0, 0.05);
  --shadow-md: rgba(0, 0, 0, 0.1);
  --shadow-lg: rgba(0, 0, 0, 0.15);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

* {
  box-sizing: border-box;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  line-height: 1.6;
}

/* Simple animations for light theme */
@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes soft-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.01);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-gentle-float {
  animation: gentle-float 3s ease-in-out infinite;
}

.animate-soft-pulse {
  animation: soft-pulse 2s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out;
}

/* Round button styles - Simple and clean */
.btn-round {
  border-radius: 9999px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px var(--shadow);
  border: none;
  font-weight: 500;
}

.btn-round:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-md);
}

.btn-round:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px var(--shadow);
}

/* Card styles - Clean and minimal */
.card-light {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 4px 20px var(--shadow);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.card-light:hover {
  box-shadow: 0 8px 25px var(--shadow-md);
  transform: translateY(-1px);
}

/* Timer circle - Clean and round */
.timer-circle {
  width: 240px;
  height: 240px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  border: 3px solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.timer-circle.active {
  animation: soft-pulse 2s infinite;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
  border-color: rgba(255, 255, 255, 1);
}

.timer-circle.paused {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 12px 32px rgba(245, 158, 11, 0.3);
  border-color: rgba(255, 255, 255, 0.9);
}

/* Simple animations for light theme */
@keyframes pulse-gentle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
}

@keyframes float-subtle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s ease-in-out infinite;
}

.animate-float-subtle {
  animation: float-subtle 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.4s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out forwards;
}

/* Button styles - Clean and simple */
.btn-primary {
  transition: all 0.2s ease;
  transform: translateY(0);
  border-radius: 9999px;
  font-weight: 500;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px var(--shadow);
}

/* Timer display - Clean typography */
.timer-display {
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.02em;
  font-weight: 300;
}

/* Icon containers - Round and clean */
.icon-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px var(--shadow);
  transition: all 0.2s ease;
}

.icon-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px var(--shadow-md);
}
