'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import moment from 'moment-timezone';

interface MilkingSession {
  id: string;
  start_time: string;
  end_time: string;
  duration: number;
  milk_quantity: number;
  created_at: string;
}

export default function HistoryPage() {
  const [sessions, setSessions] = useState<MilkingSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      fetchSessions();
    }
  }, [mounted]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/sessions`,
      );

      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }

      const data = await response.json();
      setSessions(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    return `${minutes}m ${secs}s`;
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  const formatTimeOnly = (dateString: string) => {
    return moment(dateString).format('HH:mm');
  };

  const getTotalMilk = () => {
    return sessions.reduce(
      (total, session) =>
        total + parseFloat(String(session.milk_quantity || 0)),
      0,
    );
  };

  const getAverageDuration = () => {
    if (sessions.length === 0) return 0;
    const totalDuration = sessions.reduce(
      (total, session) => total + parseInt(String(session.duration || 0)),
      0,
    );
    return Math.round(totalDuration / sessions.length);
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen'>
      {/* Header */}
      <header className='px-6 py-6'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-between'>
            <Link
              href='/'
              className='text-gray-800 hover:text-blue-600 transition-colors'
            >
              <span className='text-lg font-medium'>Milking Tracker</span>
            </Link>
            <Link
              href='/session'
              className='btn-round bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4'
            >
              Start Session
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-8'>
        <div className='max-w-4xl mx-auto'>
          {/* Page Title */}
          <div className='text-center mb-8 animate-fade-in'>
            <h1 className='text-2xl sm:text-3xl font-light text-gray-800 mb-4'>
              Session History
            </h1>
            <p className='text-gray-600'>
              Track your milking sessions and progress
            </p>
          </div>

          {/* Statistics Cards */}
          {sessions.length > 0 && (
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 animate-fade-in'>
              <div className='card-light p-6'>
                <div className='flex items-center'>
                  <div className='icon-container mr-4 bg-blue-50'>
                    <span className='text-xl'>📊</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>
                      Total Sessions
                    </p>
                    <p className='text-2xl font-semibold text-gray-800'>
                      {sessions.length}
                    </p>
                  </div>
                </div>
              </div>

              <div className='card-light p-6'>
                <div className='flex items-center'>
                  <div className='icon-container mr-4 bg-green-50'>
                    <span className='text-xl'>🥛</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>
                      Total Milk
                    </p>
                    <p className='text-2xl font-semibold text-gray-800'>
                      {getTotalMilk().toFixed(1)}L
                    </p>
                  </div>
                </div>
              </div>

              <div className='card-light p-6'>
                <div className='flex items-center'>
                  <div className='icon-container mr-4 bg-purple-50'>
                    <span className='text-xl'>⏱️</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>
                      Avg Duration
                    </p>
                    <p className='text-2xl font-semibold text-gray-800'>
                      {formatTime(getAverageDuration())}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sessions Table */}
          <div className='card-light animate-scale-in'>
            <div className='px-6 py-4 border-b border-gray-100'>
              <h2 className='text-lg font-medium text-gray-800'>
                Recent Sessions
              </h2>
            </div>

            <div className='overflow-x-auto'>
              {loading ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='w-6 h-6 border-3 border-blue-500 border-t-transparent rounded-full animate-spin'></div>
                  <span className='ml-3 text-gray-600'>
                    Loading sessions...
                  </span>
                </div>
              ) : error ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='text-center'>
                    <span className='text-3xl mb-4 block'>❌</span>
                    <p className='text-red-600 mb-4'>
                      {error}
                    </p>
                    <button
                      onClick={fetchSessions}
                      className='btn-round bg-blue-500 hover:bg-blue-600 text-white px-4 py-2'
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : sessions.length === 0 ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='text-center'>
                    <span className='text-3xl mb-4 block'>🐄</span>
                    <p className='text-gray-600 mb-4'>
                      No sessions yet
                    </p>
                    <Link
                      href='/session'
                      className='btn-round bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 inline-flex items-center space-x-2'
                    >
                      <span>🐄</span>
                      <span>Start First Session</span>
                    </Link>
                  </div>
                </div>
              ) : (
                <table className='w-full'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider'>
                        Date
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider'>
                        Start Time
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider'>
                        End Time
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider'>
                        Duration
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider'>
                        Milk Collected
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-100'>
                    {sessions.map((session) => (
                      <tr
                        key={session.id}
                        className='hover:bg-gray-50 transition-colors'
                      >
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-800'>
                          {formatDate(session.start_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-800'>
                          {formatTimeOnly(session.start_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-800'>
                          {formatTimeOnly(session.end_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-800'>
                          {formatTime(session.duration)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-800'>
                          <span className='inline-flex items-center space-x-1'>
                            <span>
                              {parseFloat(
                                String(session.milk_quantity || 0),
                              ).toFixed(1)}
                            </span>
                            <span className='text-gray-500'>
                              L
                            </span>
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
