'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import moment from 'moment-timezone';

interface MilkingSession {
  id: string;
  start_time: string;
  end_time: string;
  duration: number;
  milk_quantity: number;
  created_at: string;
}

export default function HistoryPage() {
  const [sessions, setSessions] = useState<MilkingSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      fetchSessions();
    }
  }, [mounted]);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/sessions`,
      );

      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }

      const data = await response.json();
      setSessions(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    return `${minutes}m ${secs}s`;
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format('MMM DD, YYYY');
  };

  const formatTimeOnly = (dateString: string) => {
    return moment(dateString).format('HH:mm');
  };

  const getTotalMilk = () => {
    return sessions.reduce(
      (total, session) =>
        total + parseFloat(String(session.milk_quantity || 0)),
      0,
    );
  };

  const getAverageDuration = () => {
    if (sessions.length === 0) return 0;
    const totalDuration = sessions.reduce(
      (total, session) => total + parseInt(String(session.duration || 0)),
      0,
    );
    return Math.round(totalDuration / sessions.length);
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-gray-900 dark:to-gray-800'>
      {/* Header */}
      <header className='px-4 py-6 sm:px-6 lg:px-8'>
        <div className='max-w-6xl mx-auto'>
          <div className='flex items-center justify-between'>
            <Link
              href='/'
              className='flex items-center space-x-3 text-gray-900 dark:text-white hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors'
            >
              <div className='w-8 h-8 bg-emerald-600 rounded-full flex items-center justify-center'>
                <span className='text-white text-sm font-bold'>🐄</span>
              </div>
              <span className='text-xl font-bold'>Milking Tracker</span>
            </Link>
            <Link
              href='/session'
              className='bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 flex items-center space-x-2'
            >
              <span>🐄</span>
              <span>Start Session</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-4 py-8 sm:px-6 lg:px-8'>
        <div className='max-w-6xl mx-auto'>
          {/* Page Title */}
          <div className='text-center mb-8 animate-fade-in'>
            <h1 className='text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white mb-4'>
              Milking History
            </h1>
            <p className='text-gray-600 dark:text-gray-300'>
              Track your milking sessions and monitor progress
            </p>
          </div>

          {/* Statistics Cards */}
          {sessions.length > 0 && (
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 animate-fade-in'>
              <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
                <div className='flex items-center'>
                  <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4'>
                    <span className='text-2xl'>📊</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600 dark:text-gray-400'>
                      Total Sessions
                    </p>
                    <p className='text-2xl font-bold text-gray-900 dark:text-white'>
                      {sessions.length}
                    </p>
                  </div>
                </div>
              </div>

              <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
                <div className='flex items-center'>
                  <div className='w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4'>
                    <span className='text-2xl'>🥛</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600 dark:text-gray-400'>
                      Total Milk
                    </p>
                    <p className='text-2xl font-bold text-gray-900 dark:text-white'>
                      {getTotalMilk().toFixed(1)}L
                    </p>
                  </div>
                </div>
              </div>

              <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
                <div className='flex items-center'>
                  <div className='w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4'>
                    <span className='text-2xl'>⏱️</span>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600 dark:text-gray-400'>
                      Avg Duration
                    </p>
                    <p className='text-2xl font-bold text-gray-900 dark:text-white'>
                      {formatTime(getAverageDuration())}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sessions Table */}
          <div className='bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 animate-scale-in'>
            <div className='px-6 py-4 border-b border-gray-200 dark:border-gray-700'>
              <h2 className='text-xl font-semibold text-gray-900 dark:text-white'>
                Recent Sessions
              </h2>
            </div>

            <div className='overflow-x-auto'>
              {loading ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='w-8 h-8 border-4 border-emerald-600 border-t-transparent rounded-full animate-spin'></div>
                  <span className='ml-3 text-gray-600 dark:text-gray-300'>
                    Loading sessions...
                  </span>
                </div>
              ) : error ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='text-center'>
                    <span className='text-4xl mb-4 block'>❌</span>
                    <p className='text-red-600 dark:text-red-400 mb-4'>
                      {error}
                    </p>
                    <button
                      onClick={fetchSessions}
                      className='bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-lg transition-colors'
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              ) : sessions.length === 0 ? (
                <div className='flex items-center justify-center py-12'>
                  <div className='text-center'>
                    <span className='text-4xl mb-4 block'>🐄</span>
                    <p className='text-gray-600 dark:text-gray-300 mb-4'>
                      No milking sessions yet
                    </p>
                    <Link
                      href='/session'
                      className='bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg transition-colors inline-flex items-center space-x-2'
                    >
                      <span>🐄</span>
                      <span>Start Your First Session</span>
                    </Link>
                  </div>
                </div>
              ) : (
                <table className='w-full'>
                  <thead className='bg-gray-50 dark:bg-gray-700'>
                    <tr>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                        Date
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                        Start Time
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                        End Time
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                        Duration
                      </th>
                      <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider'>
                        Milk Collected
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700'>
                    {sessions.map((session) => (
                      <tr
                        key={session.id}
                        className='hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors'
                      >
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white'>
                          {formatDate(session.start_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white'>
                          {formatTimeOnly(session.start_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white'>
                          {formatTimeOnly(session.end_time)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white'>
                          {formatTime(session.duration)}
                        </td>
                        <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white'>
                          <span className='inline-flex items-center space-x-1'>
                            <span>
                              {parseFloat(
                                String(session.milk_quantity || 0),
                              ).toFixed(1)}
                            </span>
                            <span className='text-gray-500 dark:text-gray-400'>
                              L
                            </span>
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
