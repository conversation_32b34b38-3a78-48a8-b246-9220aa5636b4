'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen'>
      {/* Header */}
      <header className='px-6 py-6'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div className='icon-container animate-gentle-float'>
                <span className='text-2xl'>🐄</span>
              </div>
              <div>
                <h1 className='text-2xl font-semibold text-gray-800'>
                  Milking Tracker
                </h1>
                <p className='text-gray-500 text-sm'>Simple & Clean</p>
              </div>
            </div>
            <Link
              href='/history'
              className='btn-round bg-white text-gray-700 hover:bg-gray-50 px-5 py-2.5 text-sm shadow-sm border border-gray-200'
            >
              📊 History
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-8'>
        <div className='max-w-4xl mx-auto'>
          {/* Hero Section */}
          <div className='text-center mb-12 animate-fade-in-up'>
            <div className='mb-8'>
              <h2 className='text-4xl sm:text-5xl font-light text-gray-800 mb-4'>
                Simple Milking Tracker
              </h2>
              <p className='text-lg text-gray-600 mb-4 max-w-xl mx-auto'>
                Track sessions with music for better results
              </p>
              <p className='text-sm text-gray-500 bg-gray-50 inline-block px-3 py-1.5 rounded-full border'>
                🎵 Music increases yield by up to 3%
              </p>
            </div>

            {/* Main Action Card */}
            <div className='max-w-md mx-auto'>
              <div className='card-light p-8 text-center'>
                <div className='icon-container mx-auto mb-6 animate-float-subtle bg-gradient-to-br from-blue-50 to-green-50'>
                  <span className='text-2xl'>🎵</span>
                </div>
                <h3 className='text-2xl font-medium text-gray-800 mb-3'>
                  Start Session
                </h3>
                <p className='text-gray-600 mb-6'>
                  Begin tracking with calming music
                </p>
                <Link
                  href='/session'
                  className='btn-round bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-8 inline-flex items-center space-x-2'
                >
                  <span>Start Session</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className='grid md:grid-cols-3 gap-6 mb-12'>
            <div className='card-light p-6 text-center'>
              <div className='icon-container mx-auto mb-4 bg-blue-50'>
                <span className='text-xl'>⏱️</span>
              </div>
              <h4 className='text-lg font-medium text-gray-800 mb-2'>
                Precise Timing
              </h4>
              <p className='text-gray-600 text-sm'>
                Track session duration accurately
              </p>
            </div>

            <div className='card-light p-6 text-center'>
              <div className='icon-container mx-auto mb-4 bg-purple-50'>
                <span className='text-xl'>🎶</span>
              </div>
              <h4 className='text-lg font-medium text-gray-800 mb-2'>
                Calming Music
              </h4>
              <p className='text-gray-600 text-sm'>
                Reduces stress and improves production
              </p>
            </div>

            <div className='card-light p-6 text-center'>
              <div className='icon-container mx-auto mb-4 bg-green-50'>
                <span className='text-xl'>📊</span>
              </div>
              <h4 className='text-lg font-medium text-gray-800 mb-2'>
                Track Progress
              </h4>
              <p className='text-gray-600 text-sm'>
                Monitor quantity and history
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className='px-4 py-8 sm:px-6 lg:px-8 mt-16'>
        <div className='max-w-4xl mx-auto text-center'>
          <p className='text-gray-500 dark:text-gray-400 text-sm'>
            Built for dairy farmers who care about their cattle&apos;s wellbeing
          </p>
        </div>
      </footer>
    </div>
  );
}
