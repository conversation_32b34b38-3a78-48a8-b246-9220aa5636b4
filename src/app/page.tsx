'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-gray-900 dark:to-gray-800'>
      {/* Header */}
      <header className='px-4 py-6 sm:px-6 lg:px-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <div className='w-10 h-10 bg-emerald-600 rounded-full flex items-center justify-center animate-bounce-subtle'>
                <span className='text-white text-xl font-bold'>🐄</span>
              </div>
              <h1 className='text-2xl font-bold text-gray-900 dark:text-white'>
                Milking Tracker
              </h1>
            </div>
            <Link
              href='/history'
              className='text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 font-medium transition-colors'
            >
              View History
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-4 py-8 sm:px-6 lg:px-8'>
        <div className='max-w-4xl mx-auto'>
          {/* Hero Section */}
          <div className='text-center mb-12 animate-fade-in'>
            <h2 className='text-4xl sm:text-5xl font-bold text-gray-900 dark:text-white mb-4'>
              Stress-Free Milking
            </h2>
            <p className='text-xl text-gray-600 dark:text-gray-300 mb-2'>
              Track milking sessions with soothing music
            </p>
            <p className='text-sm text-gray-500 dark:text-gray-400'>
              Studies show music can increase milk yield by up to 3%
            </p>
          </div>

          {/* Main Action Card */}
          <div className='max-w-md mx-auto animate-scale-in'>
            <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-gray-700'>
              <div className='text-center mb-8'>
                <div className='w-20 h-20 bg-emerald-100 dark:bg-emerald-900 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-gentle'>
                  <span className='text-3xl'>🎵</span>
                </div>
                <h3 className='text-2xl font-semibold text-gray-900 dark:text-white mb-2'>
                  Ready to Start?
                </h3>
                <p className='text-gray-600 dark:text-gray-300'>
                  Begin a new milking session with calming music
                </p>
              </div>

              <Link
                href='/session'
                className='w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 btn-primary'
              >
                <span className='text-lg'>🐄</span>
                <span className='text-lg'>Start Milking</span>
              </Link>
            </div>
          </div>

          {/* Features Grid */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 animate-fade-in'>
            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>⏱️</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Precise Timing
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Track exact duration of each milking session with our built-in
                timer
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>🎶</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Calming Music
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Soothing melodies to reduce stress and improve milk production
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>📊</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Track Progress
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Monitor milk quantity and session history for better insights
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className='px-4 py-8 sm:px-6 lg:px-8 mt-16'>
        <div className='max-w-4xl mx-auto text-center'>
          <p className='text-gray-500 dark:text-gray-400 text-sm'>
            Built for dairy farmers who care about their cattle&apos;s wellbeing
          </p>
        </div>
      </footer>
    </div>
  );
}
