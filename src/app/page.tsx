'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50'>
      {/* Header */}
      <header className='px-6 py-8'>
        <div className='max-w-6xl mx-auto'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div className='w-14 h-14 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center animate-gentle-bounce shadow-lg'>
                <span className='text-white text-2xl'>🐄</span>
              </div>
              <div>
                <h1 className='text-3xl font-bold text-gray-800'>
                  Milking Tracker
                </h1>
                <p className='text-gray-600 text-sm'>Simple & Stress-Free</p>
              </div>
            </div>
            <Link
              href='/history'
              className='btn-round bg-white text-blue-600 hover:bg-blue-50 px-6 py-3 font-medium transition-all'
            >
              📊 History
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-4'>
        <div className='max-w-6xl mx-auto'>
          {/* Hero Section */}
          <div className='text-center mb-16 animate-fade-in-up'>
            <div className='mb-8'>
              <h2 className='text-5xl sm:text-6xl font-bold text-gray-800 mb-6'>
                Stress-Free Milking
              </h2>
              <p className='text-xl text-gray-600 mb-3 max-w-2xl mx-auto'>
                Track milking sessions with soothing music for happier cows and better yields
              </p>
              <p className='text-sm text-gray-500 bg-blue-50 inline-block px-4 py-2 rounded-full'>
                🎵 Music can increase milk yield by up to 3%
              </p>
            </div>

            {/* Main Action Card */}
            <div className='max-w-lg mx-auto'>
              <div className='card-light p-12 text-center'>
                <div className='w-24 h-24 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-8 animate-gentle-bounce'>
                  <span className='text-4xl'>🎵</span>
                </div>
                <h3 className='text-3xl font-bold text-gray-800 mb-4'>
                  Ready to Start?
                </h3>
                <p className='text-gray-600 mb-8 text-lg'>
                  Begin a new milking session with calming music for optimal results
                </p>
                <Link
                  href='/session'
                  className='btn-round bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-bold py-4 px-12 text-lg inline-flex items-center space-x-3'
                >
                  <span className='text-xl'>🐄</span>
                  <span>Start Milking Session</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 animate-fade-in'>
            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>⏱️</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Precise Timing
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Track exact duration of each milking session with our built-in
                timer
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>🎶</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Calming Music
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Soothing melodies to reduce stress and improve milk production
              </p>
            </div>

            <div className='bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700'>
              <div className='w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4'>
                <span className='text-2xl'>📊</span>
              </div>
              <h4 className='text-lg font-semibold text-gray-900 dark:text-white mb-2'>
                Track Progress
              </h4>
              <p className='text-gray-600 dark:text-gray-300 text-sm'>
                Monitor milk quantity and session history for better insights
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className='px-4 py-8 sm:px-6 lg:px-8 mt-16'>
        <div className='max-w-4xl mx-auto text-center'>
          <p className='text-gray-500 dark:text-gray-400 text-sm'>
            Built for dairy farmers who care about their cattle&apos;s wellbeing
          </p>
        </div>
      </footer>
    </div>
  );
}
