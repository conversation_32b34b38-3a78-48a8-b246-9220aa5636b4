'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen'>
      {/* Header */}
      <header className='px-6 py-6'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-center'>
            <h1 className='text-2xl font-semibold text-gray-800'>
              Milking Tracker
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-8'>
        <div className='max-w-lg mx-auto'>
          {/* Main Timer Card */}
          <div className='card-light p-8 text-center mb-8'>
            <Link href='/session' className='block'>
              <div className='timer-circle cursor-pointer hover:scale-105 transition-transform'>
                <div className='text-center'>
                  <div className='text-3xl font-light text-white mb-2'>
                    00:00
                  </div>
                  <div className='text-white text-sm opacity-90'>
                    Tap to Start
                  </div>
                </div>
              </div>
            </Link>
          </div>

          {/* History Button */}
          <div className='text-center'>
            <Link
              href='/history'
              className='btn-round bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 inline-flex items-center space-x-2'
            >
              <span>📊</span>
              <span>View History</span>
            </Link>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className='px-4 py-8 sm:px-6 lg:px-8 mt-16'>
        <div className='max-w-4xl mx-auto text-center'>
          <p className='text-gray-500 dark:text-gray-400 text-sm'>
            Built for dairy farmers who care about their cattle&apos;s wellbeing
          </p>
        </div>
      </footer>
    </div>
  );
}
