'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import moment from 'moment-timezone';
import MusicPlayer from '@/components/music-player';

interface SessionData {
  start_time: string;
  end_time: string;
  duration: number;
  milk_quantity: number;
}

export default function SessionPage() {
  const router = useRouter();
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [showMilkInput, setShowMilkInput] = useState(false);
  const [milkQuantity, setMilkQuantity] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [musicError, setMusicError] = useState<string | null>(null);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isRunning && !isPaused) {
      intervalRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, isPaused]);

  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const handleStart = () => {
    setIsRunning(true);
    setIsPaused(false);
    setStartTime(new Date());
  };

  const handlePause = () => {
    setIsPaused(true);
  };

  const handleResume = () => {
    setIsPaused(false);
  };

  const handleStop = () => {
    setIsRunning(false);
    setIsPaused(false);
    setShowMilkInput(true);
  };

  const handleSubmit = async () => {
    if (!startTime || !milkQuantity) return;

    setIsSubmitting(true);

    try {
      const endTime = new Date();
      const sessionData: SessionData = {
        start_time: moment.tz(startTime, 'UTC').toISOString(),
        end_time: moment.tz(endTime, 'UTC').toISOString(),
        duration: seconds,
        milk_quantity: parseFloat(milkQuantity),
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/sessions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(sessionData),
        },
      );

      if (response.ok) {
        router.push('/history');
      } else {
        throw new Error('Failed to save session');
      }
    } catch (error) {
      console.error('Error saving session:', error);
      alert('Failed to save session. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setShowMilkInput(false);
    setSeconds(0);
    setStartTime(null);
    setMilkQuantity('');
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-gray-900 dark:to-gray-800'>
      {/* Header */}
      <header className='px-4 py-6 sm:px-6 lg:px-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-between'>
            <Link
              href='/'
              className='flex items-center space-x-3 text-gray-900 dark:text-white hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors'
            >
              <div className='w-8 h-8 bg-emerald-600 rounded-full flex items-center justify-center'>
                <span className='text-white text-sm font-bold'>🐄</span>
              </div>
              <span className='text-xl font-bold'>Milking Tracker</span>
            </Link>
            <Link
              href='/history'
              className='text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 font-medium transition-colors'
            >
              View History
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-4 py-8 sm:px-6 lg:px-8'>
        <div className='max-w-2xl mx-auto'>
          {!showMilkInput ? (
            /* Session Timer */
            <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-gray-700 animate-scale-in'>
              <div className='text-center'>
                {/* Status Indicator */}
                <div className='mb-8'>
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      isRunning && !isPaused
                        ? 'bg-green-100 dark:bg-green-900 animate-pulse-gentle'
                        : isPaused
                        ? 'bg-yellow-100 dark:bg-yellow-900'
                        : 'bg-gray-100 dark:bg-gray-700'
                    }`}
                  >
                    <span className='text-2xl'>
                      {isRunning && !isPaused ? '🎵' : isPaused ? '⏸️' : '⏱️'}
                    </span>
                  </div>
                  <h2 className='text-2xl font-bold text-gray-900 dark:text-white mb-2'>
                    {isRunning && !isPaused
                      ? 'Milking in Progress'
                      : isPaused
                      ? 'Session Paused'
                      : 'Ready to Start'}
                  </h2>
                  <p className='text-gray-600 dark:text-gray-300'>
                    {isRunning && !isPaused
                      ? 'Soothing music is playing'
                      : isPaused
                      ? 'Music paused'
                      : 'Click start to begin'}
                  </p>
                </div>

                {/* Timer Display */}
                <div className='mb-8'>
                  <div className='text-6xl sm:text-7xl font-mono font-bold text-gray-900 dark:text-white timer-display mb-2'>
                    {formatTime(seconds)}
                  </div>
                  <p className='text-sm text-gray-500 dark:text-gray-400'>
                    {seconds < 3600 ? 'MM:SS' : 'HH:MM:SS'}
                  </p>
                </div>

                {/* Control Buttons */}
                <div className='space-y-4'>
                  {!isRunning ? (
                    <button
                      onClick={handleStart}
                      className='w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 btn-primary'
                    >
                      <span className='text-lg'>▶️</span>
                      <span className='text-lg'>Start Milking</span>
                    </button>
                  ) : (
                    <div className='grid grid-cols-2 gap-4'>
                      {!isPaused ? (
                        <button
                          onClick={handlePause}
                          className='bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2'
                        >
                          <span>⏸️</span>
                          <span>Pause</span>
                        </button>
                      ) : (
                        <button
                          onClick={handleResume}
                          className='bg-green-500 hover:bg-green-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2'
                        >
                          <span>▶️</span>
                          <span>Resume</span>
                        </button>
                      )}
                      <button
                        onClick={handleStop}
                        className='bg-red-500 hover:bg-red-600 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2'
                      >
                        <span>⏹️</span>
                        <span>Stop</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            /* Milk Quantity Input */
            <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-gray-700 animate-scale-in'>
              <div className='text-center mb-8'>
                <div className='w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4'>
                  <span className='text-2xl'>🥛</span>
                </div>
                <h2 className='text-2xl font-bold text-gray-900 dark:text-white mb-2'>
                  Session Complete!
                </h2>
                <p className='text-gray-600 dark:text-gray-300'>
                  Duration: {formatTime(seconds)}
                </p>
              </div>

              <div className='space-y-6'>
                <div>
                  <label
                    htmlFor='milkQuantity'
                    className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                  >
                    Milk Collected (liters)
                  </label>
                  <input
                    type='number'
                    id='milkQuantity'
                    value={milkQuantity}
                    onChange={(e) => setMilkQuantity(e.target.value)}
                    placeholder='Enter quantity'
                    step='0.1'
                    min='0'
                    className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white'
                    autoFocus
                  />
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <button
                    onClick={handleCancel}
                    className='bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300'
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={!milkQuantity || isSubmitting}
                    className='bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2'
                  >
                    {isSubmitting ? (
                      <>
                        <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <span>💾</span>
                        <span>Save Session</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Music Player Component */}
      <MusicPlayer
        isPlaying={isRunning && !isPaused}
        onError={setMusicError}
        volume={0.3}
      />

      {/* Music Error Notification */}
      {musicError && (
        <div className='fixed top-4 right-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded-lg shadow-lg animate-fade-in'>
          <div className='flex items-center space-x-2'>
            <span>⚠️</span>
            <span className='text-sm'>{musicError}</span>
            <button
              onClick={() => setMusicError(null)}
              className='ml-2 text-yellow-600 hover:text-yellow-800 dark:text-yellow-300 dark:hover:text-yellow-100'
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
