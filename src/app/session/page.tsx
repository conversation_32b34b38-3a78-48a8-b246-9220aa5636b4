'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import moment from 'moment-timezone';
import MusicPlayer from '@/components/music-player';

interface SessionData {
  start_time: string;
  end_time: string;
  duration: number;
  milk_quantity: number;
}

export default function SessionPage() {
  const router = useRouter();
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const [showMilkInput, setShowMilkInput] = useState(false);
  const [milkQuantity, setMilkQuantity] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [musicError, setMusicError] = useState<string | null>(null);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isRunning && !isPaused) {
      intervalRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, isPaused]);

  const formatTime = (totalSeconds: number) => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const handleStart = () => {
    setIsRunning(true);
    setIsPaused(false);
    setStartTime(new Date());
  };

  const handlePause = () => {
    setIsPaused(true);
  };

  const handleResume = () => {
    setIsPaused(false);
  };

  const handleStop = () => {
    setIsRunning(false);
    setIsPaused(false);
    setShowMilkInput(true);
  };

  const handleSubmit = async () => {
    if (!startTime || !milkQuantity) return;

    setIsSubmitting(true);

    try {
      const endTime = new Date();
      const sessionData: SessionData = {
        start_time: moment.tz(startTime, 'UTC').toISOString(),
        end_time: moment.tz(endTime, 'UTC').toISOString(),
        duration: seconds,
        milk_quantity: parseFloat(milkQuantity),
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/sessions`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(sessionData),
        },
      );

      if (response.ok) {
        router.push('/history');
      } else {
        throw new Error('Failed to save session');
      }
    } catch (error) {
      console.error('Error saving session:', error);
      alert('Failed to save session. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setShowMilkInput(false);
    setSeconds(0);
    setStartTime(null);
    setMilkQuantity('');
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className='min-h-screen'>
      {/* Header */}
      <header className='px-6 py-6'>
        <div className='max-w-2xl mx-auto'>
          <div className='flex items-center justify-between'>
            <Link
              href='/'
              className='flex items-center space-x-3 text-gray-800 hover:text-blue-600 transition-colors'
            >
              <div className='icon-container w-8 h-8 bg-blue-50'>
                <span className='text-sm'>🐄</span>
              </div>
              <span className='text-lg font-medium'>Milking Tracker</span>
            </Link>
            <Link
              href='/history'
              className='btn-round bg-white text-gray-700 hover:bg-gray-50 px-4 py-2 text-sm border border-gray-200'
            >
              History
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='px-6 py-8'>
        <div className='max-w-lg mx-auto'>
          {!showMilkInput ? (
            /* Session Timer */
            <div className='card-light p-8 text-center animate-scale-in'>
              {/* Timer Circle */}
              <div className='mb-8'>
                <div
                  className={`timer-circle ${
                    isRunning && !isPaused
                      ? 'active'
                      : isPaused
                      ? 'paused'
                      : ''
                  }`}
                >
                  <div className='text-center'>
                    <div className='text-4xl sm:text-5xl font-light text-white timer-display mb-2'>
                      {formatTime(seconds)}
                    </div>
                    <div className='text-white text-sm opacity-90'>
                      {isRunning && !isPaused ? '🎵 Playing' : isPaused ? '⏸️ Paused' : '⏱️ Ready'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Text */}
              <div className='mb-8'>
                <h2 className='text-xl font-medium text-gray-800 mb-2'>
                  {isRunning && !isPaused
                    ? 'Session Active'
                    : isPaused
                    ? 'Session Paused'
                    : 'Ready to Start'}
                </h2>
                <p className='text-gray-600 text-sm'>
                  {isRunning && !isPaused
                    ? 'Music is playing'
                    : isPaused
                    ? 'Music paused'
                    : 'Click start to begin'}
                </p>
              </div>

              {/* Control Buttons */}
              <div className='space-y-4'>
                {!isRunning ? (
                  <button
                    onClick={handleStart}
                    className='btn-round bg-green-500 hover:bg-green-600 text-white font-medium py-4 px-8 w-full flex items-center justify-center space-x-2'
                  >
                    <span>▶️</span>
                    <span>Start Session</span>
                  </button>
                ) : (
                  <div className='grid grid-cols-2 gap-4'>
                    {!isPaused ? (
                      <button
                        onClick={handlePause}
                        className='btn-round bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-3 px-4 flex items-center justify-center space-x-2'
                      >
                        <span>⏸️</span>
                        <span>Pause</span>
                      </button>
                    ) : (
                      <button
                        onClick={handleResume}
                        className='btn-round bg-green-500 hover:bg-green-600 text-white font-medium py-3 px-4 flex items-center justify-center space-x-2'
                      >
                        <span>▶️</span>
                        <span>Resume</span>
                      </button>
                    )}
                    <button
                      onClick={handleStop}
                      className='btn-round bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-4 flex items-center justify-center space-x-2'
                    >
                      <span>⏹️</span>
                      <span>Stop</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Milk Quantity Input */
            <div className='card-light p-8 animate-scale-in'>
              <div className='text-center mb-8'>
                <div className='icon-container mx-auto mb-4 bg-blue-50'>
                  <span className='text-2xl'>🥛</span>
                </div>
                <h2 className='text-xl font-medium text-gray-800 mb-2'>
                  Session Complete!
                </h2>
                <p className='text-gray-600'>
                  Duration: {formatTime(seconds)}
                </p>
              </div>

              <div className='space-y-6'>
                <div>
                  <label
                    htmlFor='milkQuantity'
                    className='block text-sm font-medium text-gray-700 mb-2'
                  >
                    Milk Collected (liters)
                  </label>
                  <input
                    type='number'
                    id='milkQuantity'
                    value={milkQuantity}
                    onChange={(e) => setMilkQuantity(e.target.value)}
                    placeholder='Enter quantity'
                    step='0.1'
                    min='0'
                    className='w-full px-4 py-3 border border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50'
                    autoFocus
                  />
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <button
                    onClick={handleCancel}
                    className='btn-round bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-6'
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={!milkQuantity || isSubmitting}
                    className='btn-round bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-medium py-3 px-6 flex items-center justify-center space-x-2'
                  >
                    {isSubmitting ? (
                      <>
                        <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <span>💾</span>
                        <span>Save</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Music Player Component */}
      <MusicPlayer
        isPlaying={isRunning && !isPaused}
        onError={setMusicError}
        volume={0.3}
      />

      {/* Music Error Notification */}
      {musicError && (
        <div className='fixed top-4 right-4 bg-yellow-100 dark:bg-yellow-900 border border-yellow-400 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded-lg shadow-lg animate-fade-in'>
          <div className='flex items-center space-x-2'>
            <span>⚠️</span>
            <span className='text-sm'>{musicError}</span>
            <button
              onClick={() => setMusicError(null)}
              className='ml-2 text-yellow-600 hover:text-yellow-800 dark:text-yellow-300 dark:hover:text-yellow-100'
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
