'use client';

import { useRef, useEffect, useState } from 'react';

interface MusicPlayerProps {
  isPlaying: boolean;
  onError?: (error: string) => void;
  volume?: number;
}

export default function MusicPlayer({ isPlaying, onError, volume = 0.3 }: MusicPlayerProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleCanPlay = () => {
      setIsLoaded(true);
      setHasError(false);
    };

    const handleError = () => {
      setHasError(true);
      setIsLoaded(false);
      onError?.('Unable to load music file. Music functionality will be limited.');
    };

    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleError);

    // Set volume
    audio.volume = volume;

    return () => {
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
    };
  }, [onError, volume]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || hasError) return;

    if (isPlaying && isLoaded) {
      audio.play().catch((error) => {
        console.warn('Audio play failed:', error);
        // This is common on first load due to browser autoplay policies
        // We'll handle it gracefully
      });
    } else {
      audio.pause();
    }
  }, [isPlaying, isLoaded, hasError]);

  return (
    <div className="hidden">
      <audio
        ref={audioRef}
        loop
        preload="metadata"
      >
        {/* Multiple sources for better browser compatibility */}
        <source src="/music/calming-music.mp3" type="audio/mpeg" />
        <source src="/music/nature-sounds.mp3" type="audio/mpeg" />
        {/* Fallback for browsers that don't support MP3 */}
        <source src="/music/calming-music.ogg" type="audio/ogg" />
        Your browser does not support the audio element.
      </audio>
      
      {/* Visual indicator for music status */}
      {isPlaying && !hasError && (
        <div className="fixed bottom-4 right-4 bg-emerald-600 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-fade-in">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span className="text-sm">🎵 Music Playing</span>
        </div>
      )}
      
      {hasError && (
        <div className="fixed bottom-4 right-4 bg-yellow-600 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2 animate-fade-in">
          <span className="text-sm">🔇 Music Unavailable</span>
        </div>
      )}
    </div>
  );
}
